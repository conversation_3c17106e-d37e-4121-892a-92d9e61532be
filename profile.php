<?php
/**
 * Trang hiển thị lý lịch trích ngang sinh viên
 * <PERSON><PERSON> thống tra cứu lý lịch sinh viên - Trường Đại học Tr<PERSON> Vinh
 */

require_once 'config.php';

// Lấy ID sinh viên từ URL
$student_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($student_id <= 0) {
    header('Location: index.php?error=invalid_id');
    exit();
}

// Truy vấn thông tin sinh viên
$sql = "SELECT s.*, c.class_name, c.class_code, c.academic_year, m.major_name, m.major_code, m.faculty 
        FROM students s 
        LEFT JOIN classes c ON s.class_id = c.id 
        LEFT JOIN majors m ON s.major_id = m.id 
        WHERE s.id = :id";

$student = $database->fetch($sql, [':id' => $student_id]);

if (!$student) {
    header('Location: index.php?error=student_not_found');
    exit();
}

// Truy vấn thông tin cha mẹ
$parent_sql = "SELECT * FROM parent_info WHERE student_id = :student_id ORDER BY parent_type";
$parents = $database->fetchAll($parent_sql, [':student_id' => $student_id]);

// Tách thông tin cha và mẹ
$father = null;
$mother = null;
foreach ($parents as $parent) {
    if ($parent['parent_type'] == 'Cha') {
        $father = $parent;
    } else {
        $mother = $parent;
    }
}
?>

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lý lịch trích ngang - <?php echo htmlspecialchars($student['full_name']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <style>
        @media print {
            .no-print { display: none !important; }
            .card { box-shadow: none; border: 1px solid #ddd; }
            body { background: white; }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="bg-primary text-white py-3 no-print">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-2 text-center">
                    <img src="assets/images/logo-tvu.png" alt="Logo TVU" class="logo" style="height: 60px;">
                </div>
                <div class="col-md-8">
                    <h1 class="h3 mb-0">TRƯỜNG ĐẠI HỌC TRÀ VINH</h1>
                    <h2 class="h5 mb-0">LÝ LỊCH TRÍCH NGANG SINH VIÊN</h2>
                </div>
                <div class="col-md-2 text-end">
                    <button onclick="window.print()" class="btn btn-outline-light me-2">
                        <i class="fas fa-print me-1"></i>
                        In
                    </button>
                    <button onclick="history.back()" class="btn btn-outline-light">
                        <i class="fas fa-arrow-left me-1"></i>
                        Quay lại
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container my-4">
        <div class="card">
            <!-- Profile Header -->
            <div class="profile-header text-center">
                <div class="row align-items-center">
                    <div class="col-md-3">
                        <img src="<?php echo getPhotoUrl($student['photo']); ?>" 
                             alt="Ảnh sinh viên" class="profile-photo">
                    </div>
                    <div class="col-md-9 text-md-start">
                        <h2 class="mb-2"><?php echo htmlspecialchars($student['full_name']); ?></h2>
                        <h4 class="mb-3"><?php echo htmlspecialchars($student['student_code']); ?></h4>
                        <div class="row">
                            <div class="col-md-6">
                                <p class="mb-1"><i class="fas fa-graduation-cap me-2"></i><?php echo htmlspecialchars($student['major_name']); ?></p>
                                <p class="mb-1"><i class="fas fa-users me-2"></i><?php echo htmlspecialchars($student['class_name']); ?></p>
                            </div>
                            <div class="col-md-6">
                                <p class="mb-1"><i class="fas fa-calendar me-2"></i>Khóa: <?php echo htmlspecialchars($student['academic_year']); ?></p>
                                <p class="mb-1"><i class="fas fa-info-circle me-2"></i>Trạng thái: <?php echo htmlspecialchars($student['status']); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card-body">
                <div class="row">
                    <!-- Thông tin cá nhân -->
                    <div class="col-md-6">
                        <div class="info-section">
                            <h6><i class="fas fa-user me-2"></i>THÔNG TIN CÁ NHÂN</h6>
                            <div class="info-row">
                                <span class="info-label">Họ và tên:</span>
                                <span class="info-value"><?php echo htmlspecialchars($student['full_name']); ?></span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Mã số sinh viên:</span>
                                <span class="info-value"><?php echo htmlspecialchars($student['student_code']); ?></span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Giới tính:</span>
                                <span class="info-value"><?php echo htmlspecialchars($student['gender']); ?></span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Ngày sinh:</span>
                                <span class="info-value"><?php echo formatDate($student['birth_date']); ?></span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Nơi sinh:</span>
                                <span class="info-value"><?php echo htmlspecialchars($student['birth_place']); ?></span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Số điện thoại:</span>
                                <span class="info-value"><?php echo htmlspecialchars($student['phone']); ?></span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Địa chỉ thường trú:</span>
                                <span class="info-value"><?php echo htmlspecialchars($student['permanent_address']); ?></span>
                            </div>
                        </div>
                    </div>

                    <!-- Thông tin học tập -->
                    <div class="col-md-6">
                        <div class="info-section">
                            <h6><i class="fas fa-graduation-cap me-2"></i>THÔNG TIN HỌC TẬP</h6>
                            <div class="info-row">
                                <span class="info-label">Ngành học:</span>
                                <span class="info-value"><?php echo htmlspecialchars($student['major_name']); ?></span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Mã ngành:</span>
                                <span class="info-value"><?php echo htmlspecialchars($student['major_code']); ?></span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Khoa:</span>
                                <span class="info-value"><?php echo htmlspecialchars($student['faculty']); ?></span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Lớp học:</span>
                                <span class="info-value"><?php echo htmlspecialchars($student['class_name']); ?></span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Mã lớp:</span>
                                <span class="info-value"><?php echo htmlspecialchars($student['class_code']); ?></span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Khóa học:</span>
                                <span class="info-value"><?php echo htmlspecialchars($student['academic_year']); ?></span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Ngày nhập học:</span>
                                <span class="info-value"><?php echo formatDate($student['enrollment_date']); ?></span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Trạng thái:</span>
                                <span class="info-value">
                                    <span class="badge <?php 
                                        echo $student['status'] == 'Đang học' ? 'bg-success' : 
                                            ($student['status'] == 'Tốt nghiệp' ? 'bg-primary' : 
                                            ($student['status'] == 'Thôi học' ? 'bg-danger' : 'bg-warning text-dark')); 
                                    ?>">
                                        <?php echo htmlspecialchars($student['status']); ?>
                                    </span>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Thông tin gia đình -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="info-section">
                            <h6><i class="fas fa-home me-2"></i>THÔNG TIN GIA ĐÌNH</h6>
                            
                            <div class="row">
                                <!-- Thông tin cha -->
                                <div class="col-md-6">
                                    <h6 class="text-primary mt-3 mb-3">Thông tin cha</h6>
                                    <?php if ($father): ?>
                                        <div class="info-row">
                                            <span class="info-label">Họ và tên:</span>
                                            <span class="info-value"><?php echo htmlspecialchars($father['full_name']); ?></span>
                                        </div>
                                        <div class="info-row">
                                            <span class="info-label">Năm sinh:</span>
                                            <span class="info-value"><?php echo htmlspecialchars($father['birth_year']); ?></span>
                                        </div>
                                        <div class="info-row">
                                            <span class="info-label">Nghề nghiệp:</span>
                                            <span class="info-value"><?php echo htmlspecialchars($father['occupation']); ?></span>
                                        </div>
                                        <div class="info-row">
                                            <span class="info-label">Số điện thoại:</span>
                                            <span class="info-value"><?php echo htmlspecialchars($father['phone']); ?></span>
                                        </div>
                                        <div class="info-row">
                                            <span class="info-label">Địa chỉ liên hệ:</span>
                                            <span class="info-value"><?php echo htmlspecialchars($father['contact_address']); ?></span>
                                        </div>
                                    <?php else: ?>
                                        <p class="text-muted">Chưa có thông tin</p>
                                    <?php endif; ?>
                                </div>

                                <!-- Thông tin mẹ -->
                                <div class="col-md-6">
                                    <h6 class="text-primary mt-3 mb-3">Thông tin mẹ</h6>
                                    <?php if ($mother): ?>
                                        <div class="info-row">
                                            <span class="info-label">Họ và tên:</span>
                                            <span class="info-value"><?php echo htmlspecialchars($mother['full_name']); ?></span>
                                        </div>
                                        <div class="info-row">
                                            <span class="info-label">Năm sinh:</span>
                                            <span class="info-value"><?php echo htmlspecialchars($mother['birth_year']); ?></span>
                                        </div>
                                        <div class="info-row">
                                            <span class="info-label">Nghề nghiệp:</span>
                                            <span class="info-value"><?php echo htmlspecialchars($mother['occupation']); ?></span>
                                        </div>
                                        <div class="info-row">
                                            <span class="info-label">Số điện thoại:</span>
                                            <span class="info-value"><?php echo htmlspecialchars($mother['phone']); ?></span>
                                        </div>
                                        <div class="info-row">
                                            <span class="info-label">Địa chỉ liên hệ:</span>
                                            <span class="info-value"><?php echo htmlspecialchars($mother['contact_address']); ?></span>
                                        </div>
                                    <?php else: ?>
                                        <p class="text-muted">Chưa có thông tin</p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Thông tin hệ thống -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="info-section">
                            <h6><i class="fas fa-info-circle me-2"></i>THÔNG TIN HỆ THỐNG</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="info-row">
                                        <span class="info-label">Ngày tạo hồ sơ:</span>
                                        <span class="info-value"><?php echo formatDate($student['created_at'], 'd/m/Y H:i'); ?></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-row">
                                        <span class="info-label">Cập nhật lần cuối:</span>
                                        <span class="info-value"><?php echo formatDate($student['updated_at'], 'd/m/Y H:i'); ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action buttons -->
        <div class="text-center mt-4 no-print">
            <button onclick="window.print()" class="btn btn-primary me-2">
                <i class="fas fa-print me-1"></i>
                In lý lịch
            </button>
            <button onclick="history.back()" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>
                Quay lại
            </button>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-white text-center py-3 mt-5 no-print">
        <div class="container">
            <p class="mb-0">&copy; 2024 Trường Đại học Trà Vinh. Phát triển bởi Khoa Kỹ thuật và Công nghệ.</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

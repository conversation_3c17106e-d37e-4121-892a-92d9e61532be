# Dữ liệu mẫu để test chương trình
# Sao chép và dán vào chương trình khi chọn "Nhập danh sách sinh viên"

S<PERSON> lượng sinh viên: 5

Sinh viên 1:
Tên lớp: CNTT K65
Mã lớp: CNTT01
Ngành học: Công nghệ thông tin
Họ tên sinh viên: <PERSON><PERSON><PERSON> Van An
Mã số sinh viên: SV001
Năm sinh: 2003
Điểm xét tuyển: 8.5

Sinh viên 2:
Tên lớp: CNTT K65
Mã lớp: CNTT01
Ngành học: Công nghệ thông tin
Họ tên sinh viên: Tran Thi Binh
Mã số sinh viên: SV002
Năm sinh: 2003
Điểm xét tuyển: 7.8

Sinh viên 3:
Tên lớp: KTPM K65
Mã lớp: KTPM01
Ngành học: <PERSON><PERSON> thuật phần mềm
Họ tên sinh viên: <PERSON> Van Cuong
Mã số sinh viên: SV003
Năm sinh: 2002
Điểm xét tuyển: 9.2

Sinh viên 4:
Tên lớp: KTPM K65
Mã lớp: KTPM01
Ngành học: Kỹ thuật phần mềm
Họ tên sinh viên: Pham Thi Dung
Mã số sinh viên: SV004
Năm sinh: 2003
Điểm xét tuyển: 8.0

Sinh viên 5:
Tên lớp: ATTT K65
Mã lớp: ATTT01
Ngành học: An toàn thông tin
Họ tên sinh viên: Hoang Van Em
Mã số sinh viên: SV005
Năm sinh: 2002
Điểm xét tuyển: 8.7

# Các test case để thử nghiệm:

Test tìm kiếm theo mã sinh viên:
- SV001 (có)
- SV999 (không có)

Test tìm kiếm theo mã lớp:
- CNTT01 (2 sinh viên)
- KTPM01 (2 sinh viên)
- ATTT01 (1 sinh viên)
- XXXX01 (không có)

Test tìm kiếm theo năm sinh:
- 2002 (2 sinh viên)
- 2003 (3 sinh viên)
- 2001 (không có)

Test tìm kiếm theo điểm:
- 8.0 - 9.0 (4 sinh viên)
- 9.0 - 10.0 (1 sinh viên)
- 6.0 - 7.0 (không có)

Test xóa sinh viên:
- SV003 (có thể xóa)
- SV999 (không thể xóa)

Test sắp xếp:
- Danh sách sẽ được sắp xếp theo mã sinh viên: SV001, SV002, SV003, SV004, SV005

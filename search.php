<?php
/**
 * File xử lý tìm kiếm sinh viên
 * <PERSON>ệ thống tra cứu lý lịch sinh viên - Trường Đạ<PERSON> học Trà Vin<PERSON>
 */

require_once 'config.php';

// Lấy thông tin tìm kiếm từ form
$search_type = isset($_GET['search_type']) ? sanitize($_GET['search_type']) : '';
$keyword = isset($_GET['keyword']) ? sanitize($_GET['keyword']) : '';
$major = isset($_GET['major']) ? sanitize($_GET['major']) : '';
$gender = isset($_GET['gender']) ? sanitize($_GET['gender']) : '';
$status = isset($_GET['status']) ? sanitize($_GET['status']) : '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;

// Validate input
if (empty($search_type) || empty($keyword)) {
    header('Location: index.php?error=missing_params');
    exit();
}

// Tính toán phân trang
$offset = ($page - 1) * RECORDS_PER_PAGE;

// Xây dựng câu truy vấn SQL
$sql = "SELECT s.*, c.class_name, c.class_code, c.academic_year, m.major_name, m.major_code 
        FROM students s 
        LEFT JOIN classes c ON s.class_id = c.id 
        LEFT JOIN majors m ON s.major_id = m.id 
        WHERE 1=1";

$params = [];
$count_sql = "SELECT COUNT(*) FROM students s 
              LEFT JOIN classes c ON s.class_id = c.id 
              LEFT JOIN majors m ON s.major_id = m.id 
              WHERE 1=1";

// Thêm điều kiện tìm kiếm chính
switch ($search_type) {
    case 'student_code':
        $sql .= " AND s.student_code LIKE :keyword";
        $count_sql .= " AND s.student_code LIKE :keyword";
        $params[':keyword'] = '%' . $keyword . '%';
        break;
        
    case 'full_name':
        $sql .= " AND s.full_name LIKE :keyword";
        $count_sql .= " AND s.full_name LIKE :keyword";
        $params[':keyword'] = '%' . $keyword . '%';
        break;
        
    case 'class':
        $sql .= " AND (c.class_code LIKE :keyword OR c.class_name LIKE :keyword)";
        $count_sql .= " AND (c.class_code LIKE :keyword OR c.class_name LIKE :keyword)";
        $params[':keyword'] = '%' . $keyword . '%';
        break;
        
    case 'academic_year':
        $sql .= " AND c.academic_year LIKE :keyword";
        $count_sql .= " AND c.academic_year LIKE :keyword";
        $params[':keyword'] = '%' . $keyword . '%';
        break;
        
    default:
        header('Location: index.php?error=invalid_search_type');
        exit();
}

// Thêm điều kiện tìm kiếm nâng cao
if (!empty($major)) {
    $sql .= " AND s.major_id = :major";
    $count_sql .= " AND s.major_id = :major";
    $params[':major'] = $major;
}

if (!empty($gender)) {
    $sql .= " AND s.gender = :gender";
    $count_sql .= " AND s.gender = :gender";
    $params[':gender'] = $gender;
}

if (!empty($status)) {
    $sql .= " AND s.status = :status";
    $count_sql .= " AND s.status = :status";
    $params[':status'] = $status;
}

// Thêm sắp xếp và phân trang
$sql .= " ORDER BY s.student_code ASC LIMIT :limit OFFSET :offset";
$params[':limit'] = RECORDS_PER_PAGE;
$params[':offset'] = $offset;

// Thực hiện truy vấn
try {
    // Đếm tổng số bản ghi
    $total_records = $database->count($count_sql, array_diff_key($params, [':limit' => '', ':offset' => '']));
    $total_pages = ceil($total_records / RECORDS_PER_PAGE);
    
    // Lấy dữ liệu sinh viên
    $students = $database->fetchAll($sql, $params);
    
} catch (Exception $e) {
    $error_message = "Lỗi truy vấn dữ liệu: " . $e->getMessage();
}

// Tạo URL cho phân trang
$base_url = "search.php?search_type=" . urlencode($search_type) . 
           "&keyword=" . urlencode($keyword) . 
           "&major=" . urlencode($major) . 
           "&gender=" . urlencode($gender) . 
           "&status=" . urlencode($status);

// Hàm lấy badge trạng thái
function getStatusBadge($status) {
    $badges = [
        'Đang học' => 'bg-success',
        'Tốt nghiệp' => 'bg-primary',
        'Thôi học' => 'bg-danger',
        'Bảo lưu' => 'bg-warning text-dark'
    ];
    
    $class = isset($badges[$status]) ? $badges[$status] : 'bg-secondary';
    return "<span class='badge {$class}'>{$status}</span>";
}

// Hàm lấy badge giới tính
function getGenderBadge($gender) {
    $class = ($gender == 'Nam') ? 'bg-info' : 'bg-pink';
    return "<span class='badge {$class}'>{$gender}</span>";
}
?>

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kết quả tìm kiếm - Tra cứu lý lịch sinh viên</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="bg-primary text-white py-3">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-2 text-center">
                    <img src="assets/images/logo-tvu.png" alt="Logo TVU" class="logo" style="height: 60px;">
                </div>
                <div class="col-md-8">
                    <h1 class="h3 mb-0">TRƯỜNG ĐẠI HỌC TRÀ VINH</h1>
                    <h2 class="h5 mb-0">KẾT QUẢ TÌM KIẾM SINH VIÊN</h2>
                </div>
                <div class="col-md-2 text-end">
                    <a href="index.php" class="btn btn-outline-light">
                        <i class="fas fa-home me-1"></i>
                        Trang chủ
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container my-4">
        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo $error_message; ?>
            </div>
        <?php else: ?>
            <!-- Thông tin tìm kiếm -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5 class="mb-1">
                                <i class="fas fa-search text-primary me-2"></i>
                                Kết quả tìm kiếm: "<?php echo htmlspecialchars($keyword); ?>"
                            </h5>
                            <p class="text-muted mb-0">
                                Tìm theo: <?php 
                                $search_labels = [
                                    'student_code' => 'Mã số sinh viên',
                                    'full_name' => 'Họ và tên',
                                    'class' => 'Lớp học',
                                    'academic_year' => 'Khóa học'
                                ];
                                echo $search_labels[$search_type];
                                ?>
                                | Tìm thấy: <strong><?php echo $total_records; ?></strong> sinh viên
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="index.php" class="btn btn-outline-primary">
                                <i class="fas fa-search me-1"></i>
                                Tìm kiếm mới
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <?php if (empty($students)): ?>
                <div class="alert alert-warning text-center">
                    <i class="fas fa-exclamation-circle fa-3x mb-3"></i>
                    <h4>Không tìm thấy kết quả</h4>
                    <p>Không có sinh viên nào phù hợp với từ khóa tìm kiếm của bạn.</p>
                    <a href="index.php" class="btn btn-primary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Quay lại tìm kiếm
                    </a>
                </div>
            <?php else: ?>
                <!-- Danh sách sinh viên -->
                <div class="row">
                    <?php foreach ($students as $student): ?>
                        <div class="col-md-6 mb-4">
                            <div class="card student-card h-100 fade-in">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-3 text-center">
                                            <img src="<?php echo getPhotoUrl($student['photo']); ?>" 
                                                 alt="Ảnh sinh viên" class="student-photo">
                                        </div>
                                        <div class="col-9">
                                            <div class="student-info">
                                                <h5><?php echo htmlspecialchars($student['full_name']); ?></h5>
                                                <p class="text-muted mb-1">
                                                    <i class="fas fa-id-card me-1"></i>
                                                    <?php echo htmlspecialchars($student['student_code']); ?>
                                                </p>
                                                <p class="text-muted mb-1">
                                                    <i class="fas fa-users me-1"></i>
                                                    <?php echo htmlspecialchars($student['class_name']); ?>
                                                </p>
                                                <p class="text-muted mb-2">
                                                    <i class="fas fa-graduation-cap me-1"></i>
                                                    <?php echo htmlspecialchars($student['major_name']); ?>
                                                </p>
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <div>
                                                        <?php echo getGenderBadge($student['gender']); ?>
                                                        <?php echo getStatusBadge($student['status']); ?>
                                                    </div>
                                                    <a href="profile.php?id=<?php echo $student['id']; ?>" 
                                                       class="btn btn-sm btn-primary">
                                                        <i class="fas fa-eye me-1"></i>
                                                        Xem chi tiết
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Phân trang -->
                <?php if ($total_pages > 1): ?>
                    <div class="d-flex justify-content-center mt-4">
                        <?php echo createPagination($page, $total_pages, $base_url); ?>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        <?php endif; ?>
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-white text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0">&copy; 2024 Trường Đại học Trà Vinh. Phát triển bởi Khoa Kỹ thuật và Công nghệ.</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

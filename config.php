<?php
/**
 * File cấu hình kết nối cơ sở dữ liệu
 * Hệ thống tra cứu lý lịch sinh viên - Trường Đại học Trà Vinh
 */

// C<PERSON><PERSON> hình database
define('DB_HOST', 'localhost');
define('DB_NAME', 'student_records');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// Cấu hình ứng dụng
define('APP_NAME', 'Hệ thống tra cứu lý lịch sinh viên');
define('SCHOOL_NAME', 'Trường Đại học Trà Vinh');
define('UPLOAD_PATH', 'uploads/photos/');
define('DEFAULT_PHOTO', 'assets/images/default-avatar.png');

// <PERSON><PERSON><PERSON> hình phân trang
define('RECORDS_PER_PAGE', 10);

class Database {
    private $host = DB_HOST;
    private $db_name = DB_NAME;
    private $username = DB_USER;
    private $password = DB_PASS;
    private $charset = DB_CHARSET;
    private $pdo;
    
    /**
     * Kết nối cơ sở dữ liệu
     */
    public function connect() {
        $this->pdo = null;
        
        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];
            
            $this->pdo = new PDO($dsn, $this->username, $this->password, $options);
            
        } catch(PDOException $e) {
            echo "Lỗi kết nối: " . $e->getMessage();
            die();
        }
        
        return $this->pdo;
    }
    
    /**
     * Thực hiện truy vấn SELECT
     */
    public function query($sql, $params = []) {
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch(PDOException $e) {
            echo "Lỗi truy vấn: " . $e->getMessage();
            return false;
        }
    }
    
    /**
     * Lấy một bản ghi
     */
    public function fetch($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt ? $stmt->fetch() : false;
    }
    
    /**
     * Lấy nhiều bản ghi
     */
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt ? $stmt->fetchAll() : false;
    }
    
    /**
     * Đếm số bản ghi
     */
    public function count($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt ? $stmt->fetchColumn() : 0;
    }
    
    /**
     * Lấy ID của bản ghi vừa thêm
     */
    public function lastInsertId() {
        return $this->pdo->lastInsertId();
    }
    
    /**
     * Bắt đầu transaction
     */
    public function beginTransaction() {
        return $this->pdo->beginTransaction();
    }
    
    /**
     * Commit transaction
     */
    public function commit() {
        return $this->pdo->commit();
    }
    
    /**
     * Rollback transaction
     */
    public function rollback() {
        return $this->pdo->rollback();
    }
}

/**
 * Hàm tiện ích
 */

/**
 * Làm sạch dữ liệu đầu vào
 */
function sanitize($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

/**
 * Chuyển đổi định dạng ngày
 */
function formatDate($date, $format = 'd/m/Y') {
    if (empty($date)) return '';
    return date($format, strtotime($date));
}

/**
 * Tạo URL ảnh đại diện
 */
function getPhotoUrl($photo) {
    if (empty($photo) || !file_exists($photo)) {
        return DEFAULT_PHOTO;
    }
    return $photo;
}

/**
 * Tạo phân trang
 */
function createPagination($current_page, $total_pages, $base_url) {
    $pagination = '<nav aria-label="Phân trang"><ul class="pagination justify-content-center">';
    
    // Nút Previous
    if ($current_page > 1) {
        $pagination .= '<li class="page-item"><a class="page-link" href="' . $base_url . '&page=' . ($current_page - 1) . '">Trước</a></li>';
    }
    
    // Các số trang
    $start = max(1, $current_page - 2);
    $end = min($total_pages, $current_page + 2);
    
    for ($i = $start; $i <= $end; $i++) {
        $active = ($i == $current_page) ? 'active' : '';
        $pagination .= '<li class="page-item ' . $active . '"><a class="page-link" href="' . $base_url . '&page=' . $i . '">' . $i . '</a></li>';
    }
    
    // Nút Next
    if ($current_page < $total_pages) {
        $pagination .= '<li class="page-item"><a class="page-link" href="' . $base_url . '&page=' . ($current_page + 1) . '">Sau</a></li>';
    }
    
    $pagination .= '</ul></nav>';
    return $pagination;
}

// Khởi tạo kết nối database toàn cục
$database = new Database();
$db = $database->connect();
?>

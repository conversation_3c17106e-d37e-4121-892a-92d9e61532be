# 🚀 Hướng dẫn cài đặt và chạy Website Tra cứu Sinh viên

## Phương án 1: Sử dụng XAMPP (Khuyến nghị)

### Bước 1: Tải và cài đặt XAMPP
1. Truy cập: https://www.apachefriends.org/download.html
2. Tải **XAMPP for Windows** (PHP 8.0+)
3. Chạy file installer và cài đặt vào `C:\xampp`
4. Trong quá trình cài đặt, chọn: Apache, MySQL, PHP, phpMyAdmin

### Bước 2: Khởi động XAMPP
1. Mở **XAMPP Control Panel** (Start Menu → XAMPP → XAMPP Control Panel)
2. Nhấn **Start** cho **Apache** và **MySQL**
3. Đợi đến khi cả hai service hiển thị màu xanh

### Bước 3: Copy website vào XAMPP
1. Mở thư mục `C:\xampp\htdocs`
2. <PERSON><PERSON><PERSON> thư mục mới tên `student-records`
3. <PERSON><PERSON> tất cả files từ thư mục dự án vào `C:\xampp\htdocs\student-records\`

### Bước 4: Tạo database
1. Mở trình duyệt, truy cập: http://localhost/phpmyadmin
2. Nhấn **New** để tạo database mới
3. Tên database: `student_records`
4. Collation: `utf8mb4_unicode_ci`
5. Nhấn **Create**

### Bước 5: Import dữ liệu
1. Chọn database `student_records` vừa tạo
2. Nhấn tab **Import**
3. Nhấn **Choose File** và chọn file `database.sql`
4. Nhấn **Go** để import

### Bước 6: Cấu hình kết nối
1. Mở file `C:\xampp\htdocs\student-records\config.php`
2. Kiểm tra thông tin kết nối (thường không cần thay đổi):
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'student_records');
define('DB_USER', 'root');
define('DB_PASS', '');
```

### Bước 7: Chạy website
1. Mở trình duyệt
2. Truy cập: http://localhost/student-records/
3. Test kết nối: http://localhost/student-records/test_connection.php

---

## Phương án 2: Sử dụng PHP Built-in Server (Đơn giản hơn)

### Bước 1: Tải PHP Standalone
1. Truy cập: https://windows.php.net/download/
2. Tải **VS16 x64 Thread Safe** (ZIP)
3. Giải nén vào thư mục `C:\php`
4. Thêm `C:\php` vào PATH environment variable

### Bước 2: Cài đặt MySQL
1. Tải MySQL Community Server: https://dev.mysql.com/downloads/mysql/
2. Cài đặt MySQL với password root (ghi nhớ password)
3. Hoặc tải MySQL Workbench để quản lý dễ hơn

### Bước 3: Tạo database
```sql
CREATE DATABASE student_records CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE student_records;
-- Copy nội dung file database.sql và chạy
```

### Bước 4: Cấu hình PHP
1. Copy `C:\php\php.ini-development` thành `C:\php\php.ini`
2. Mở `php.ini` và bỏ comment (xóa dấu ;) các dòng:
```ini
extension=pdo_mysql
extension=mysqli
```

### Bước 5: Chạy PHP Server
1. Mở Command Prompt trong thư mục dự án
2. Chạy lệnh:
```bash
php -S localhost:8000
```
3. Truy cập: http://localhost:8000

---

## Phương án 3: Sử dụng Docker (Cho người có kinh nghiệm)

### Tạo file docker-compose.yml:
```yaml
version: '3.8'
services:
  web:
    image: php:8.1-apache
    ports:
      - "8080:80"
    volumes:
      - .:/var/www/html
    depends_on:
      - db
  
  db:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: student_records
    ports:
      - "3306:3306"
    volumes:
      - ./database.sql:/docker-entrypoint-initdb.d/database.sql
```

### Chạy:
```bash
docker-compose up -d
```

---

## 🧪 Kiểm tra sau khi cài đặt

### 1. Test kết nối database
- Truy cập: http://localhost/student-records/test_connection.php
- Hoặc: http://localhost:8000/test_connection.php

### 2. Test website chính
- Truy cập: http://localhost/student-records/
- Hoặc: http://localhost:8000/

### 3. Test tìm kiếm
- Thử tìm kiếm với từ khóa: "Nguyễn"
- Thử tìm kiếm mã sinh viên: "2051120001"
- Thử tìm kiếm lớp: "CNTT20A"

---

## 🐛 Xử lý lỗi thường gặp

### Lỗi kết nối database:
- Kiểm tra MySQL đã khởi động chưa
- Kiểm tra thông tin trong config.php
- Kiểm tra database đã được tạo chưa

### Lỗi PHP:
- Kiểm tra extension PDO đã được bật chưa
- Kiểm tra PHP version >= 7.4

### Lỗi 404:
- Kiểm tra đường dẫn file
- Kiểm tra Apache/PHP server đã chạy chưa

---

## 📞 Hỗ trợ

Nếu gặp vấn đề, hãy:
1. Kiểm tra log lỗi trong XAMPP Control Panel
2. Chạy test_connection.php để kiểm tra database
3. Kiểm tra browser console để xem lỗi JavaScript

**Chúc bạn thành công! 🎉**

#include "student_manager.h"

int main() {
    StudentManager manager;
    int choice;
    string filename = "students.dat";
    
    do {
        displayMenu();
        choice = getChoice();
        
        switch (choice) {
            case 1: {
                clearScreen();
                cout << "=== NHAP DANH SACH SINH VIEN ===" << endl;
                manager.inputStudentList();
                pauseScreen();
                break;
            }
            
            case 2: {
                clearScreen();
                cout << "=== DANH SACH SINH VIEN ===" << endl;
                manager.displayAllStudents();
                pauseScreen();
                break;
            }
            
            case 3: {
                clearScreen();
                cout << "=== THEM SINH VIEN MOI ===" << endl;
                manager.inputSingleStudent();
                pauseScreen();
                break;
            }
            
            case 4: {
                clearScreen();
                cout << "=== XOA SINH VIEN ===" << endl;
                string studentID;
                cout << "Nhap ma so sinh vien can xoa: ";
                getline(cin, studentID);
                
                if (manager.removeStudent(studentID)) {
                    cout << "Da xoa sinh vien co ma so: " << studentID << endl;
                } else {
                    cout << "Khong tim thay sinh vien co ma so: " << studentID << endl;
                }
                pauseScreen();
                break;
            }
            
            case 5: {
                clearScreen();
                cout << "=== SAP XEP DANH SACH ===" << endl;
                manager.sortByStudentID();
                cout << "Da sap xep danh sach theo ma sinh vien!" << endl;
                pauseScreen();
                break;
            }
            
            case 6: {
                clearScreen();
                cout << "=== TIM KIEM THEO MA SINH VIEN ===" << endl;
                string studentID;
                cout << "Nhap ma so sinh vien can tim: ";
                getline(cin, studentID);
                
                Node* found = manager.findByStudentID(studentID);
                if (found != nullptr) {
                    cout << "Tim thay sinh vien:" << endl;
                    manager.displayHeader();
                    cout << setw(3) << "1";
                    manager.displayStudent(found->data);
                    cout << string(120, '-') << endl;
                } else {
                    cout << "Khong tim thay sinh vien co ma so: " << studentID << endl;
                }
                pauseScreen();
                break;
            }
            
            case 7: {
                clearScreen();
                cout << "=== TIM KIEM THEO MA LOP ===" << endl;
                string classCode;
                cout << "Nhap ma lop can tim: ";
                getline(cin, classCode);
                manager.findByClassCode(classCode);
                pauseScreen();
                break;
            }
            
            case 8: {
                clearScreen();
                cout << "=== TIM KIEM THEO NAM SINH ===" << endl;
                int year;
                cout << "Nhap nam sinh can tim: ";
                cin >> year;
                cin.ignore();
                manager.findByBirthYear(year);
                pauseScreen();
                break;
            }
            
            case 9: {
                clearScreen();
                cout << "=== TIM KIEM THEO DIEM SO ===" << endl;
                float minScore, maxScore;
                cout << "Nhap diem toi thieu: ";
                cin >> minScore;
                cout << "Nhap diem toi da: ";
                cin >> maxScore;
                cin.ignore();
                manager.findByScore(minScore, maxScore);
                pauseScreen();
                break;
            }
            
            case 10: {
                clearScreen();
                cout << "=== LUU DANH SACH VAO FILE ===" << endl;
                string saveFile;
                cout << "Nhap ten file (Enter de dung mac dinh 'students.dat'): ";
                getline(cin, saveFile);
                if (saveFile.empty()) {
                    saveFile = filename;
                }
                manager.saveToFile(saveFile);
                pauseScreen();
                break;
            }
            
            case 11: {
                clearScreen();
                cout << "=== DOC DANH SACH TU FILE ===" << endl;
                string loadFile;
                cout << "Nhap ten file (Enter de dung mac dinh 'students.dat'): ";
                getline(cin, loadFile);
                if (loadFile.empty()) {
                    loadFile = filename;
                }
                manager.loadFromFile(loadFile);
                pauseScreen();
                break;
            }
            
            case 0: {
                cout << "Cam on ban da su dung chuong trinh!" << endl;
                break;
            }
            
            default: {
                cout << "Lua chon khong hop le! Vui long chon lai." << endl;
                pauseScreen();
                break;
            }
        }
        
        if (choice != 0) {
            clearScreen();
        }
        
    } while (choice != 0);
    
    return 0;
}

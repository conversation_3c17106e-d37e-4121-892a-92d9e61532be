# Chương trình Quản lý Thông tin Sinh viên

## <PERSON><PERSON> tả
Chương trình quản lý thông tin sinh viên sử dụng danh sách liên kết đơn được viết bằng C++. Chương trình cho phép quản lý thông tin sinh viên bao gồm tên lớp, mã lớ<PERSON>, ng<PERSON><PERSON> họ<PERSON>, họ tên, mã số sinh viên, năm sinh và điểm xét tuyển.

## Cấu trúc dữ liệu
- **Student**: Struct chứa thông tin sinh viên
- **Node**: Node của danh sách liên kết đơn
- **StudentManager**: Class quản lý danh sách sinh viên

## Chức năng chính

### 1. Quản lý danh sách
- Nhập danh sách sinh viên
- <PERSON><PERSON><PERSON> danh sách sinh viên
- Thêm sinh viên vào danh sách đã sắp xếp
- X<PERSON>a sinh viên theo mã số sinh viên
- Sắp xếp danh sách theo mã số sinh viên

### 2. Tìm kiếm
- Tìm kiếm theo mã số sinh viên
- Tìm kiếm theo mã lớp
- Tìm kiếm theo năm sinh
- Tìm kiếm theo khoảng điểm số

### 3. Lưu trữ dữ liệu
- Lưu danh sách vào file nhị phân
- Đọc danh sách từ file nhị phân

## Cách biên dịch và chạy

### Sử dụng Makefile (khuyến nghị)
```bash
# Biên dịch chương trình
make

# Chạy chương trình
make run

# Xóa các file build
make clean
```

### Biên dịch thủ công
```bash
# Biên dịch
g++ -std=c++11 -Wall -Wextra -o student_manager main.cpp student_manager.cpp

# Chạy
./student_manager
```

## Cách sử dụng

1. **Khởi động chương trình**: Chạy file thực thi
2. **Chọn chức năng**: Nhập số tương ứng với chức năng muốn sử dụng
3. **Nhập dữ liệu**: Làm theo hướng dẫn trên màn hình
4. **Lưu dữ liệu**: Sử dụng chức năng lưu file để bảo toàn dữ liệu

## Menu chương trình
```
1. Nhập danh sách sinh viên
2. Xuất danh sách sinh viên  
3. Thêm một sinh viên (đã sắp xếp)
4. Xóa sinh viên theo mã số
5. Sắp xếp danh sách theo mã sinh viên
6. Tìm kiếm sinh viên theo mã số
7. Tìm kiếm sinh viên theo mã lớp
8. Tìm kiếm sinh viên theo năm sinh
9. Tìm kiếm sinh viên theo điểm số
10. Lưu danh sách vào file
11. Đọc danh sách từ file
0. Thoát chương trình
```

## Thông tin sinh viên
Mỗi sinh viên bao gồm các thông tin sau:
- **Tên lớp**: Tên của lớp học
- **Mã lớp**: Mã định danh của lớp
- **Ngành học**: Ngành học của sinh viên
- **Họ tên sinh viên**: Họ và tên đầy đủ
- **Mã số sinh viên**: Mã định danh duy nhất
- **Năm sinh**: Năm sinh của sinh viên
- **Điểm xét tuyển**: Điểm số xét tuyển vào trường

## File dữ liệu
- File mặc định: `students.dat`
- Định dạng: File nhị phân
- Có thể chỉ định tên file khác khi lưu/đọc

## Yêu cầu hệ thống
- Compiler C++ hỗ trợ C++11 trở lên
- Hệ điều hành: Windows/Linux/macOS
- RAM: Tối thiểu 512MB

## Tác giả
Chương trình được phát triển để minh họa việc sử dụng danh sách liên kết đơn trong quản lý dữ liệu.

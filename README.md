# Hệ thống tra cứu lý lịch sinh viên - Trường Đại học Trà Vinh

## M<PERSON> tả dự án

Ứng dụng web phục vụ tra cứu lý lịch trích ngang của sinh viên tại Trường Đại học Trà Vinh. <PERSON>ệ thống cho phép tìm kiếm thông tin sinh viên theo nhiều tiêu chí khác nhau và hiển thị lý lịch trích ngang đầy đủ.

## Tính năng chính

### 1. Quản lý thông tin sinh viên
- Lưu trữ thông tin cá nhân: h<PERSON> tê<PERSON>, mã số sinh viên, gi<PERSON><PERSON>, ng<PERSON><PERSON>h, n<PERSON><PERSON> sinh, đ<PERSON>a chỉ, đi<PERSON><PERSON> thoại, hình ảnh
- Thông tin học tập: ng<PERSON><PERSON> họ<PERSON>, l<PERSON><PERSON> họ<PERSON>, <PERSON><PERSON><PERSON><PERSON> h<PERSON>, tr<PERSON><PERSON> thá<PERSON> học tập
- Thông tin gia đình: thông tin cha mẹ (<PERSON><PERSON>ê<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>hi<PERSON>, đ<PERSON><PERSON> chỉ, đ<PERSON><PERSON><PERSON> tho<PERSON>)

### 2. Chức năng tìm kiếm
- Tìm kiếm theo tên sinh viên
- Tìm kiếm theo mã số sinh viên
- Tìm kiếm theo lớp học
- Tìm kiếm theo khóa học
- Tìm kiếm nâng cao với bộ lọc: ngành học, giới tính, trạng thái

### 3. Hiển thị kết quả
- Danh sách sinh viên với thông tin tóm tắt
- Phân trang kết quả tìm kiếm
- Xem chi tiết lý lịch trích ngang từng sinh viên
- Chức năng in lý lịch

## Công nghệ sử dụng

- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap 5
- **Backend**: PHP 7.4+
- **Database**: MySQL 5.7+
- **Icons**: Font Awesome 6
- **Responsive**: Bootstrap Grid System

## Cấu trúc thư mục

```
lkdon/
├── assets/
│   ├── css/
│   │   └── style.css          # Stylesheet chính
│   └── images/
│       ├── default-avatar.png # Ảnh đại diện mặc định
│       └── logo-tvu.png       # Logo trường (cần thêm)
├── uploads/
│   └── photos/                # Thư mục chứa ảnh sinh viên
├── config.php                 # Cấu hình database và functions
├── database.sql               # Script tạo database và dữ liệu mẫu
├── index.php                  # Trang chủ - form tìm kiếm
├── search.php                 # Xử lý tìm kiếm và hiển thị kết quả
├── profile.php                # Hiển thị lý lịch trích ngang chi tiết
└── README.md                  # Tài liệu hướng dẫn
```

## Hướng dẫn cài đặt

### 1. Yêu cầu hệ thống
- Web server (Apache/Nginx)
- PHP 7.4 trở lên
- MySQL 5.7 trở lên
- Extension PHP: PDO, PDO_MySQL

### 2. Cài đặt database
1. Tạo database mới trong MySQL:
```sql
CREATE DATABASE student_records CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. Import file database.sql:
```bash
mysql -u username -p student_records < database.sql
```

### 3. Cấu hình ứng dụng
1. Mở file `config.php`
2. Cập nhật thông tin kết nối database:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'student_records');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

### 4. Cấu hình web server
1. Copy toàn bộ files vào thư mục web root
2. Đảm bảo thư mục `uploads/photos/` có quyền ghi
3. Truy cập ứng dụng qua browser

## Hướng dẫn sử dụng

### 1. Tìm kiếm sinh viên
1. Truy cập trang chủ
2. Chọn loại tìm kiếm (mã SV, tên, lớp, khóa)
3. Nhập từ khóa tìm kiếm
4. (Tùy chọn) Sử dụng tìm kiếm nâng cao với các bộ lọc
5. Nhấn "Tìm kiếm"

### 2. Xem kết quả
- Danh sách sinh viên hiển thị dạng card với thông tin cơ bản
- Sử dụng phân trang để duyệt qua nhiều kết quả
- Nhấn "Xem chi tiết" để xem lý lịch đầy đủ

### 3. Xem lý lịch trích ngang
- Hiển thị đầy đủ thông tin cá nhân, học tập và gia đình
- Sử dụng nút "In lý lịch" để in tài liệu
- Nút "Quay lại" để trở về kết quả tìm kiếm

## Dữ liệu mẫu

Hệ thống đã được tích hợp sẵn dữ liệu mẫu bao gồm:
- 7 ngành học thuộc các khoa khác nhau
- 8 lớp học từ khóa 2020-2024 và 2021-2025
- 7 sinh viên mẫu với đầy đủ thông tin
- Thông tin cha mẹ cho từng sinh viên

## Tính năng bảo mật

- Sử dụng PDO Prepared Statements để tránh SQL Injection
- Sanitize dữ liệu đầu vào với htmlspecialchars()
- Validate tham số URL và form input
- Error handling và redirect an toàn

## Tính năng responsive

- Giao diện tương thích với mobile, tablet, desktop
- Bootstrap 5 responsive grid system
- Optimized cho việc in ấn (print styles)

## Hỗ trợ và phát triển

### Mở rộng tính năng
- Thêm chức năng quản lý sinh viên (CRUD)
- Tích hợp hệ thống đăng nhập/phân quyền
- Export dữ liệu ra Excel/PDF
- Upload và quản lý ảnh sinh viên
- Lịch sử tra cứu và thống kê

### Liên hệ
- Email: <EMAIL>
- Website: https://www.tvu.edu.vn

## License

© 2024 Trường Đại học Trà Vinh. All rights reserved.

#ifndef STUDENT_MANAGER_H
#define STUDENT_MANAGER_H

#include <iostream>
#include <fstream>
#include <string>
#include <iomanip>
using namespace std;

// C<PERSON>u trúc thông tin sinh viên
struct Student {
    string className;        // Tên lớp
    string classCode;        // Mã lớp
    string major;            // Ngành học
    string fullName;         // Họ tên sinh viên
    string studentID;        // Mã số sinh viên
    int birthYear;           // Năm sinh
    float admissionScore;    // Điểm xét tuyển
};

// Node của danh sách liên kết đơn
struct Node {
    Student data;
    Node* next;
    
    Node(const Student& student) : data(student), next(nullptr) {}
};

// Lớp quản lý danh sách sinh viên
class StudentManager {
private:
    Node* head;
    
public:
    // Constructor và Destructor
    StudentManager();
    ~StudentManager();
    
    // <PERSON><PERSON><PERSON> chức năng cơ bản
    void addStudent(const Student& student);
    void addStudentSorted(const Student& student);
    bool removeStudent(const string& studentID);
    void sortByStudentID();
    void displayAllStudents() const;
    bool isEmpty() const;
    void clearList();
    
    // Các chức năng tìm kiếm
    Node* findByStudentID(const string& studentID) const;
    void findByClassCode(const string& classCode) const;
    void findByBirthYear(int year) const;
    void findByScore(float minScore, float maxScore) const;
    
    // Các chức năng file I/O
    bool saveToFile(const string& filename) const;
    bool loadFromFile(const string& filename);
    
    // Các chức năng nhập xuất
    void inputStudentList();
    void inputSingleStudent();
    
    // Utility functions
    void displayStudent(const Student& student) const;
    void displayHeader() const;
};

// Các hàm tiện ích
void clearScreen();
void pauseScreen();
Student createStudent();
void displayMenu();
int getChoice();

#endif

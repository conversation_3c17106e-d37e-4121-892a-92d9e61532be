#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Demo simulation của chương trình quản lý sinh viên C++
<PERSON><PERSON> phỏng cách hoạt động của chương trình thực tế
"""

import os
import pickle
from typing import List, Optional

class Student:
    def __init__(self, class_name="", class_code="", major="", full_name="", 
                 student_id="", birth_year=0, admission_score=0.0):
        self.class_name = class_name
        self.class_code = class_code
        self.major = major
        self.full_name = full_name
        self.student_id = student_id
        self.birth_year = birth_year
        self.admission_score = admission_score

class Node:
    def __init__(self, student: Student):
        self.data = student
        self.next = None

class StudentManager:
    def __init__(self):
        self.head = None
    
    def add_student(self, student: Student):
        new_node = Node(student)
        if self.head is None:
            self.head = new_node
        else:
            current = self.head
            while current.next is not None:
                current = current.next
            current.next = new_node
    
    def add_student_sorted(self, student: Student):
        new_node = Node(student)
        
        if self.head is None or student.student_id < self.head.data.student_id:
            new_node.next = self.head
            self.head = new_node
            return
        
        current = self.head
        while current.next is not None and current.next.data.student_id < student.student_id:
            current = current.next
        
        new_node.next = current.next
        current.next = new_node
    
    def remove_student(self, student_id: str) -> bool:
        if self.head is None:
            return False
        
        if self.head.data.student_id == student_id:
            self.head = self.head.next
            return True
        
        current = self.head
        while current.next is not None and current.next.data.student_id != student_id:
            current = current.next
        
        if current.next is not None:
            current.next = current.next.next
            return True
        
        return False
    
    def sort_by_student_id(self):
        if self.head is None or self.head.next is None:
            return
        
        swapped = True
        while swapped:
            swapped = False
            current = self.head
            
            while current.next is not None:
                if current.data.student_id > current.next.data.student_id:
                    # Hoán đổi dữ liệu
                    current.data, current.next.data = current.next.data, current.data
                    swapped = True
                current = current.next
    
    def display_all_students(self):
        if self.head is None:
            print("Danh sach sinh vien trong!")
            return
        
        self.display_header()
        current = self.head
        count = 1
        
        while current is not None:
            self.display_student_row(count, current.data)
            current = current.next
            count += 1
        print("-" * 120)
    
    def display_header(self):
        print("-" * 120)
        print(f"{'STT':>3} {'Ten lop':>12} {'Ma lop':>10} {'Nganh hoc':>15} {'Ho ten SV':>20} {'Ma SV':>12} {'Nam sinh':>8} {'Diem XT':>8}")
        print("-" * 120)
    
    def display_student_row(self, stt: int, student: Student):
        print(f"{stt:>3} {student.class_name:>12} {student.class_code:>10} {student.major:>15} "
              f"{student.full_name:>20} {student.student_id:>12} {student.birth_year:>8} {student.admission_score:>8.2f}")
    
    def find_by_student_id(self, student_id: str) -> Optional[Node]:
        current = self.head
        while current is not None:
            if current.data.student_id == student_id:
                return current
            current = current.next
        return None
    
    def find_by_class_code(self, class_code: str):
        found = False
        current = self.head
        
        print(f"Ket qua tim kiem theo ma lop: {class_code}")
        self.display_header()
        
        count = 1
        while current is not None:
            if current.data.class_code == class_code:
                self.display_student_row(count, current.data)
                found = True
                count += 1
            current = current.next
        
        if not found:
            print(f"Khong tim thay sinh vien nao co ma lop: {class_code}")
        print("-" * 120)
    
    def find_by_birth_year(self, year: int):
        found = False
        current = self.head
        
        print(f"Ket qua tim kiem theo nam sinh: {year}")
        self.display_header()
        
        count = 1
        while current is not None:
            if current.data.birth_year == year:
                self.display_student_row(count, current.data)
                found = True
                count += 1
            current = current.next
        
        if not found:
            print(f"Khong tim thay sinh vien nao sinh nam: {year}")
        print("-" * 120)
    
    def find_by_score(self, min_score: float, max_score: float):
        found = False
        current = self.head
        
        print(f"Ket qua tim kiem theo diem ({min_score} - {max_score}):")
        self.display_header()
        
        count = 1
        while current is not None:
            if min_score <= current.data.admission_score <= max_score:
                self.display_student_row(count, current.data)
                found = True
                count += 1
            current = current.next
        
        if not found:
            print(f"Khong tim thay sinh vien nao co diem trong khoang: {min_score} - {max_score}")
        print("-" * 120)
    
    def save_to_file(self, filename: str) -> bool:
        try:
            students = []
            current = self.head
            while current is not None:
                students.append(current.data)
                current = current.next
            
            with open(filename, 'wb') as f:
                pickle.dump(students, f)
            
            print(f"Da luu {len(students)} sinh vien vao file: {filename}")
            return True
        except Exception as e:
            print(f"Khong the luu file: {e}")
            return False
    
    def load_from_file(self, filename: str) -> bool:
        try:
            with open(filename, 'rb') as f:
                students = pickle.load(f)
            
            # Xóa danh sách hiện tại
            self.head = None
            
            # Thêm sinh viên từ file
            for student in students:
                self.add_student(student)
            
            print(f"Da doc {len(students)} sinh vien tu file: {filename}")
            return True
        except FileNotFoundError:
            print(f"Khong tim thay file: {filename}")
            return False
        except Exception as e:
            print(f"Khong the doc file: {e}")
            return False

def clear_screen():
    os.system('cls' if os.name == 'nt' else 'clear')

def pause_screen():
    input("\nNhan Enter de tiep tuc...")

def create_student() -> Student:
    student = Student()
    student.class_name = input("Ten lop: ")
    student.class_code = input("Ma lop: ")
    student.major = input("Nganh hoc: ")
    student.full_name = input("Ho ten sinh vien: ")
    student.student_id = input("Ma so sinh vien: ")
    student.birth_year = int(input("Nam sinh: "))
    student.admission_score = float(input("Diem xet tuyen: "))
    return student

def display_menu():
    print("\n" + "=" * 60)
    print("         CHUONG TRINH QUAN LY SINH VIEN")
    print("=" * 60)
    print("1. Nhap danh sach sinh vien")
    print("2. Xuat danh sach sinh vien")
    print("3. Them mot sinh vien (da sap xep)")
    print("4. Xoa sinh vien theo ma so")
    print("5. Sap xep danh sach theo ma sinh vien")
    print("6. Tim kiem sinh vien theo ma so")
    print("7. Tim kiem sinh vien theo ma lop")
    print("8. Tim kiem sinh vien theo nam sinh")
    print("9. Tim kiem sinh vien theo diem so")
    print("10. Luu danh sach vao file")
    print("11. Doc danh sach tu file")
    print("12. Tao du lieu mau (demo)")
    print("0. Thoat chuong trinh")
    print("-" * 60)

def create_sample_data(manager: StudentManager):
    """Tạo dữ liệu mẫu để demo"""
    sample_students = [
        Student("CNTT K65", "CNTT01", "Công nghệ thông tin", "Nguyen Van An", "SV001", 2003, 8.5),
        Student("CNTT K65", "CNTT01", "Công nghệ thông tin", "Tran Thi Binh", "SV002", 2003, 7.8),
        Student("KTPM K65", "KTPM01", "Kỹ thuật phần mềm", "Le Van Cuong", "SV003", 2002, 9.2),
        Student("KTPM K65", "KTPM01", "Kỹ thuật phần mềm", "Pham Thi Dung", "SV004", 2003, 8.0),
        Student("ATTT K65", "ATTT01", "An toàn thông tin", "Hoang Van Em", "SV005", 2002, 8.7)
    ]
    
    for student in sample_students:
        manager.add_student(student)
    
    print("Da tao 5 sinh vien mau!")

def main():
    manager = StudentManager()
    filename = "students.dat"
    
    print("=== DEMO CHUONG TRINH QUAN LY SINH VIEN ===")
    print("(Mô phỏng chương trình C++ bằng Python)")
    
    while True:
        display_menu()
        try:
            choice = int(input("Lua chon cua ban: "))
        except ValueError:
            print("Vui long nhap so!")
            pause_screen()
            continue
        
        if choice == 1:
            clear_screen()
            print("=== NHAP DANH SACH SINH VIEN ===")
            try:
                n = int(input("Nhap so luong sinh vien: "))
                for i in range(n):
                    print(f"\nNhap thong tin sinh vien thu {i + 1}:")
                    student = create_student()
                    manager.add_student(student)
                print(f"Da nhap thanh cong {n} sinh vien!")
            except ValueError:
                print("So luong khong hop le!")
            pause_screen()
        
        elif choice == 2:
            clear_screen()
            print("=== DANH SACH SINH VIEN ===")
            manager.display_all_students()
            pause_screen()
        
        elif choice == 3:
            clear_screen()
            print("=== THEM SINH VIEN MOI ===")
            student = create_student()
            manager.add_student_sorted(student)
            print("Da them sinh vien thanh cong!")
            pause_screen()
        
        elif choice == 4:
            clear_screen()
            print("=== XOA SINH VIEN ===")
            student_id = input("Nhap ma so sinh vien can xoa: ")
            if manager.remove_student(student_id):
                print(f"Da xoa sinh vien co ma so: {student_id}")
            else:
                print(f"Khong tim thay sinh vien co ma so: {student_id}")
            pause_screen()
        
        elif choice == 5:
            clear_screen()
            print("=== SAP XEP DANH SACH ===")
            manager.sort_by_student_id()
            print("Da sap xep danh sach theo ma sinh vien!")
            pause_screen()
        
        elif choice == 6:
            clear_screen()
            print("=== TIM KIEM THEO MA SINH VIEN ===")
            student_id = input("Nhap ma so sinh vien can tim: ")
            found = manager.find_by_student_id(student_id)
            if found:
                print("Tim thay sinh vien:")
                manager.display_header()
                manager.display_student_row(1, found.data)
                print("-" * 120)
            else:
                print(f"Khong tim thay sinh vien co ma so: {student_id}")
            pause_screen()
        
        elif choice == 7:
            clear_screen()
            print("=== TIM KIEM THEO MA LOP ===")
            class_code = input("Nhap ma lop can tim: ")
            manager.find_by_class_code(class_code)
            pause_screen()
        
        elif choice == 8:
            clear_screen()
            print("=== TIM KIEM THEO NAM SINH ===")
            try:
                year = int(input("Nhap nam sinh can tim: "))
                manager.find_by_birth_year(year)
            except ValueError:
                print("Nam sinh khong hop le!")
            pause_screen()
        
        elif choice == 9:
            clear_screen()
            print("=== TIM KIEM THEO DIEM SO ===")
            try:
                min_score = float(input("Nhap diem toi thieu: "))
                max_score = float(input("Nhap diem toi da: "))
                manager.find_by_score(min_score, max_score)
            except ValueError:
                print("Diem so khong hop le!")
            pause_screen()
        
        elif choice == 10:
            clear_screen()
            print("=== LUU DANH SACH VAO FILE ===")
            save_file = input(f"Nhap ten file (Enter de dung mac dinh '{filename}'): ")
            if not save_file:
                save_file = filename
            manager.save_to_file(save_file)
            pause_screen()
        
        elif choice == 11:
            clear_screen()
            print("=== DOC DANH SACH TU FILE ===")
            load_file = input(f"Nhap ten file (Enter de dung mac dinh '{filename}'): ")
            if not load_file:
                load_file = filename
            manager.load_from_file(load_file)
            pause_screen()
        
        elif choice == 12:
            clear_screen()
            print("=== TAO DU LIEU MAU ===")
            create_sample_data(manager)
            pause_screen()
        
        elif choice == 0:
            print("Cam on ban da su dung chuong trinh!")
            break
        
        else:
            print("Lua chon khong hop le! Vui long chon lai.")
            pause_screen()
        
        if choice != 0:
            clear_screen()

if __name__ == "__main__":
    main()

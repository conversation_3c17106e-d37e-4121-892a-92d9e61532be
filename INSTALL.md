# Hướng dẫn cài đặt và biên dịch

## Y<PERSON>u cầu hệ thống
- Hệ điều hành: Windows 10/11, Linux, macOS
- Compiler C++ hỗ trợ C++11 trở lên

## Cài đặt Compiler trên Windows

### Tùy chọn 1: MinGW-w64 (<PERSON><PERSON><PERSON><PERSON><PERSON> nghị)
1. Tải xuống từ: https://www.mingw-w64.org/downloads/
2. Hoặc cài đặt qua MSYS2:
   - Tải MSYS2: https://www.msys2.org/
   - Mở MSYS2 terminal và chạy:
     ```bash
     pacman -S mingw-w64-x86_64-gcc
     ```
3. Thêm đường dẫn vào PATH environment variable
4. <PERSON><PERSON><PERSON> tra: `g++ --version`

### Tùy chọn 2: Microsoft Visual Studio
1. Tải Visual Studio Community (miễn phí): https://visualstudio.microsoft.com/
2. Chọn "Desktop development with C++" workload
3. Mở "Developer Command Prompt for VS"
4. <PERSON><PERSON><PERSON> tra: `cl`

### Tùy chọn 3: LLVM/Clang
1. T<PERSON><PERSON> từ: https://releases.llvm.org/
2. Cài đặt và thêm vào PATH
3. Kiểm tra: `clang++ --version`

## Biên dịch chương trình

### Trên Windows
```batch
# Sử dụng script tự động
build.bat

# Hoặc thủ công với g++
g++ -std=c++11 -Wall -Wextra -o student_manager.exe main.cpp student_manager.cpp

# Hoặc với Visual Studio
cl /EHsc /Fe:student_manager.exe main.cpp student_manager.cpp

# Hoặc với clang++
clang++ -std=c++11 -Wall -Wextra -o student_manager.exe main.cpp student_manager.cpp
```

### Trên Linux/macOS
```bash
# Sử dụng Makefile
make

# Hoặc thủ công
g++ -std=c++11 -Wall -Wextra -o student_manager main.cpp student_manager.cpp

# Với clang++
clang++ -std=c++11 -Wall -Wextra -o student_manager main.cpp student_manager.cpp
```

## Chạy chương trình

### Windows
```batch
student_manager.exe
```

### Linux/macOS
```bash
./student_manager
```

## Xử lý sự cố

### Lỗi "command not found" hoặc "not recognized"
- Đảm bảo compiler đã được cài đặt
- Kiểm tra PATH environment variable
- Khởi động lại terminal/command prompt

### Lỗi biên dịch
- Đảm bảo tất cả file source (.cpp, .h) có trong cùng thư mục
- Kiểm tra phiên bản C++ (cần C++11 trở lên)
- Đọc thông báo lỗi để xác định vấn đề cụ thể

### Lỗi runtime
- Đảm bảo có quyền ghi file trong thư mục chương trình
- Kiểm tra định dạng dữ liệu nhập vào
- File dữ liệu (.dat) phải ở cùng thư mục với executable

## Cấu trúc file
```
project/
├── main.cpp              # File chính
├── student_manager.cpp   # Implementation
├── student_manager.h     # Header file
├── Makefile             # Build script cho Linux/macOS
├── build.bat            # Build script cho Windows
├── README.md            # Hướng dẫn sử dụng
├── INSTALL.md           # Hướng dẫn cài đặt (file này)
└── students.dat         # File dữ liệu (tạo tự động)
```

## Liên hệ hỗ trợ
Nếu gặp vấn đề trong quá trình cài đặt hoặc biên dịch, vui lòng kiểm tra:
1. Phiên bản compiler
2. Cấu hình PATH environment
3. Quyền truy cập file/thư mục
4. Thông báo lỗi chi tiết

-- <PERSON><PERSON> sở dữ liệu tra cứu lý lịch sinh viên Trường Đại học Trà Vinh
-- Tạo database
CREATE DATABASE IF NOT EXISTS student_records CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE student_records;

-- <PERSON><PERSON><PERSON> ngành học
CREATE TABLE majors (
    id INT AUTO_INCREMENT PRIMARY KEY,
    major_code VARCHAR(10) NOT NULL UNIQUE,
    major_name VARCHAR(100) NOT NULL,
    faculty VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON><PERSON>ng lớp học
CREATE TABLE classes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    class_code VARCHAR(20) NOT NULL UNIQUE,
    class_name VARCHAR(100) NOT NULL,
    major_id INT,
    academic_year VARCHAR(10) NOT NULL, -- <PERSON><PERSON><PERSON><PERSON> học (VD: 2020-2024)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (major_id) REFERENCES majors(id)
);

-- <PERSON><PERSON>ng sinh viên
CREATE TABLE students (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_code VARCHAR(20) NOT NULL UNIQUE,
    full_name VARCHAR(100) NOT NULL,
    gender ENUM('Nam', 'Nữ') NOT NULL,
    birth_date DATE NOT NULL,
    birth_place VARCHAR(200),
    permanent_address TEXT,
    phone VARCHAR(15),
    photo VARCHAR(255), -- Đường dẫn đến file ảnh
    class_id INT,
    major_id INT,
    enrollment_date DATE,
    status ENUM('Đang học', 'Tốt nghiệp', 'Thôi học', 'Bảo lưu') DEFAULT 'Đang học',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (class_id) REFERENCES classes(id),
    FOREIGN KEY (major_id) REFERENCES majors(id)
);

-- Bảng thông tin cha mẹ
CREATE TABLE parent_info (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    parent_type ENUM('Cha', 'Mẹ') NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    birth_year YEAR,
    occupation VARCHAR(100),
    contact_address TEXT,
    phone VARCHAR(15),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE
);

-- Thêm dữ liệu mẫu cho ngành học
INSERT INTO majors (major_code, major_name, faculty) VALUES
('CNTT', 'Công nghệ thông tin', 'Khoa Kỹ thuật và Công nghệ'),
('KTPM', 'Kỹ thuật phần mềm', 'Khoa Kỹ thuật và Công nghệ'),
('QTKD', 'Quản trị kinh doanh', 'Khoa Kinh tế'),
('TCNH', 'Tài chính ngân hàng', 'Khoa Kinh tế'),
('NNTA', 'Ngôn ngữ Anh', 'Khoa Ngoại ngữ'),
('SPVH', 'Sư phạm Văn học', 'Khoa Sư phạm'),
('SPLS', 'Sư phạm Lịch sử', 'Khoa Sư phạm');

-- Thêm dữ liệu mẫu cho lớp học
INSERT INTO classes (class_code, class_name, major_id, academic_year) VALUES
('CNTT20A', 'Công nghệ thông tin 20A', 1, '2020-2024'),
('CNTT20B', 'Công nghệ thông tin 20B', 1, '2020-2024'),
('CNTT21A', 'Công nghệ thông tin 21A', 1, '2021-2025'),
('KTPM20A', 'Kỹ thuật phần mềm 20A', 2, '2020-2024'),
('KTPM21A', 'Kỹ thuật phần mềm 21A', 2, '2021-2025'),
('QTKD20A', 'Quản trị kinh doanh 20A', 3, '2020-2024'),
('TCNH20A', 'Tài chính ngân hàng 20A', 4, '2020-2024'),
('NNTA21A', 'Ngôn ngữ Anh 21A', 5, '2021-2025');

-- Thêm dữ liệu mẫu sinh viên
INSERT INTO students (student_code, full_name, gender, birth_date, birth_place, permanent_address, phone, photo, class_id, major_id, enrollment_date) VALUES
('2051120001', 'Nguyễn Văn An', 'Nam', '2002-03-15', 'Trà Vinh', '123 Đường Nguyễn Đáng, TP. Trà Vinh', '0901234567', 'photos/2051120001.jpg', 1, 1, '2020-09-01'),
('2051120002', 'Trần Thị Bình', 'Nữ', '2002-07-22', 'Vĩnh Long', '456 Đường Lê Lợi, TP. Vĩnh Long', '0912345678', 'photos/2051120002.jpg', 1, 1, '2020-09-01'),
('2051120003', 'Lê Minh Cường', 'Nam', '2002-01-10', 'Cần Thơ', '789 Đường 3/2, Q. Ninh Kiều, TP. Cần Thơ', '0923456789', 'photos/2051120003.jpg', 2, 1, '2020-09-01'),
('2051220001', 'Phạm Thị Dung', 'Nữ', '2002-05-18', 'Sóc Trăng', '321 Đường Trần Hưng Đạo, TP. Sóc Trăng', '0934567890', 'photos/2051220001.jpg', 4, 2, '2020-09-01'),
('2051320001', 'Hoàng Văn Em', 'Nam', '2002-09-03', 'An Giang', '654 Đường Tôn Đức Thắng, TP. Long Xuyên', '0945678901', 'photos/2051320001.jpg', 6, 3, '2020-09-01'),
('2151120001', 'Võ Thị Phương', 'Nữ', '2003-02-14', 'Trà Vinh', '987 Đường Điện Biên Phủ, TP. Trà Vinh', '0956789012', 'photos/2151120001.jpg', 3, 1, '2021-09-01'),
('2151220001', 'Đặng Minh Quang', 'Nam', '2003-06-25', 'Bến Tre', '147 Đường Hùng Vương, TP. Bến Tre', '0967890123', 'photos/2151220001.jpg', 5, 2, '2021-09-01');

-- Thêm thông tin cha mẹ mẫu
INSERT INTO parent_info (student_id, parent_type, full_name, birth_year, occupation, contact_address, phone) VALUES
-- Thông tin cha mẹ của Nguyễn Văn An
(1, 'Cha', 'Nguyễn Văn Bình', 1975, 'Nông dân', '123 Đường Nguyễn Đáng, TP. Trà Vinh', '0901111111'),
(1, 'Mẹ', 'Trần Thị Cúc', 1978, 'Nội trợ', '123 Đường Nguyễn Đáng, TP. Trà Vinh', '0901111112'),
-- Thông tin cha mẹ của Trần Thị Bình
(2, 'Cha', 'Trần Văn Dũng', 1973, 'Giáo viên', '456 Đường Lê Lợi, TP. Vĩnh Long', '0912222221'),
(2, 'Mẹ', 'Lê Thị Em', 1976, 'Y tá', '456 Đường Lê Lợi, TP. Vĩnh Long', '0912222222'),
-- Thông tin cha mẹ của Lê Minh Cường
(3, 'Cha', 'Lê Văn Phúc', 1974, 'Kỹ sư', '789 Đường 3/2, Q. Ninh Kiều, TP. Cần Thơ', '0923333331'),
(3, 'Mẹ', 'Nguyễn Thị Giang', 1977, 'Kế toán', '789 Đường 3/2, Q. Ninh Kiều, TP. Cần Thơ', '0923333332'),
-- Thông tin cha mẹ của Phạm Thị Dung
(4, 'Cha', 'Phạm Văn Hải', 1972, 'Công nhân', '321 Đường Trần Hưng Đạo, TP. Sóc Trăng', '0934444441'),
(4, 'Mẹ', 'Võ Thị Lan', 1975, 'Bán hàng', '321 Đường Trần Hưng Đạo, TP. Sóc Trăng', '0934444442'),
-- Thông tin cha mẹ của Hoàng Văn Em
(5, 'Cha', 'Hoàng Văn Minh', 1971, 'Cán bộ', '654 Đường Tôn Đức Thắng, TP. Long Xuyên', '0945555551'),
(5, 'Mẹ', 'Trần Thị Nga', 1974, 'Nhân viên văn phòng', '654 Đường Tôn Đức Thắng, TP. Long Xuyên', '0945555552'),
-- Thông tin cha mẹ của Võ Thị Phương
(6, 'Cha', 'Võ Văn Ơn', 1976, 'Thợ điện', '987 Đường Điện Biên Phủ, TP. Trà Vinh', '0956666661'),
(6, 'Mẹ', 'Lê Thị Phấn', 1979, 'Nông dân', '987 Đường Điện Biên Phủ, TP. Trà Vinh', '0956666662'),
-- Thông tin cha mẹ của Đặng Minh Quang
(7, 'Cha', 'Đặng Văn Quý', 1973, 'Tài xế', '147 Đường Hùng Vương, TP. Bến Tre', '0967777771'),
(7, 'Mẹ', 'Huỳnh Thị Sáu', 1976, 'Bán hàng', '147 Đường Hùng Vương, TP. Bến Tre', '0967777772');

-- Tạo index để tối ưu tìm kiếm
CREATE INDEX idx_student_code ON students(student_code);
CREATE INDEX idx_student_name ON students(full_name);
CREATE INDEX idx_class_code ON classes(class_code);
CREATE INDEX idx_academic_year ON classes(academic_year);
CREATE INDEX idx_major_code ON majors(major_code);

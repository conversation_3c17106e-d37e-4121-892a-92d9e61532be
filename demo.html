<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DEMO - Tra c<PERSON><PERSON> lý lịch sinh viên - Trường <PERSON> h<PERSON> Vinh</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="bg-primary text-white py-3">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-2 text-center">
                    <div style="width: 60px; height: 60px; background: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                        <i class="fas fa-university text-primary fa-2x"></i>
                    </div>
                </div>
                <div class="col-md-10">
                    <h1 class="h3 mb-0">TRƯỜNG ĐẠI HỌC TRÀ VINH</h1>
                    <h2 class="h5 mb-0">HỆ THỐNG TRA CỨU LÝ LỊCH SINH VIÊN</h2>
                    <small class="text-warning">🚀 DEMO VERSION - Cần cài đặt PHP & MySQL để chạy đầy đủ</small>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container my-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow">
                    <div class="card-header bg-light">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-search me-2"></i>
                            Tra cứu thông tin sinh viên
                        </h3>
                    </div>
                    <div class="card-body">
                        <form id="demoForm">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="search_type" class="form-label">Loại tìm kiếm:</label>
                                    <select class="form-select" id="search_type" name="search_type" required>
                                        <option value="">-- Chọn loại tìm kiếm --</option>
                                        <option value="student_code">Mã số sinh viên</option>
                                        <option value="full_name">Họ và tên</option>
                                        <option value="class">Lớp học</option>
                                        <option value="academic_year">Khóa học</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="keyword" class="form-label">Từ khóa tìm kiếm:</label>
                                    <input type="text" class="form-control" id="keyword" name="keyword" 
                                           placeholder="Nhập từ khóa..." required>
                                </div>
                            </div>

                            <!-- Tìm kiếm nâng cao -->
                            <div class="row mb-3" id="advanced_search" style="display: none;">
                                <div class="col-md-4">
                                    <label for="major" class="form-label">Ngành học:</label>
                                    <select class="form-select" id="major" name="major">
                                        <option value="">-- Tất cả ngành --</option>
                                        <option value="1">Công nghệ thông tin</option>
                                        <option value="2">Kỹ thuật phần mềm</option>
                                        <option value="3">Quản trị kinh doanh</option>
                                        <option value="4">Tài chính ngân hàng</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="gender" class="form-label">Giới tính:</label>
                                    <select class="form-select" id="gender" name="gender">
                                        <option value="">-- Tất cả --</option>
                                        <option value="Nam">Nam</option>
                                        <option value="Nữ">Nữ</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="status" class="form-label">Trạng thái:</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="">-- Tất cả --</option>
                                        <option value="Đang học">Đang học</option>
                                        <option value="Tốt nghiệp">Tốt nghiệp</option>
                                        <option value="Thôi học">Thôi học</option>
                                        <option value="Bảo lưu">Bảo lưu</option>
                                    </select>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between align-items-center">
                                <button type="button" class="btn btn-outline-secondary" id="toggleAdvanced">
                                    <i class="fas fa-cog me-1"></i>
                                    Tìm kiếm nâng cao
                                </button>
                                <div>
                                    <button type="reset" class="btn btn-outline-warning me-2">
                                        <i class="fas fa-undo me-1"></i>
                                        Làm mới
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-1"></i>
                                        Tìm kiếm (DEMO)
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Demo Results -->
                <div class="card mt-4" id="demoResults" style="display: none;">
                    <div class="card-body">
                        <h5 class="mb-3">
                            <i class="fas fa-search text-primary me-2"></i>
                            Kết quả tìm kiếm DEMO
                        </h5>
                        
                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <div class="card student-card h-100">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-3 text-center">
                                                <div class="student-photo bg-secondary d-flex align-items-center justify-content-center text-white">
                                                    <i class="fas fa-user fa-2x"></i>
                                                </div>
                                            </div>
                                            <div class="col-9">
                                                <div class="student-info">
                                                    <h5>Nguyễn Văn An</h5>
                                                    <p class="text-muted mb-1">
                                                        <i class="fas fa-id-card me-1"></i>
                                                        2051120001
                                                    </p>
                                                    <p class="text-muted mb-1">
                                                        <i class="fas fa-users me-1"></i>
                                                        Công nghệ thông tin 20A
                                                    </p>
                                                    <p class="text-muted mb-2">
                                                        <i class="fas fa-graduation-cap me-1"></i>
                                                        Công nghệ thông tin
                                                    </p>
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <div>
                                                            <span class="badge bg-info">Nam</span>
                                                            <span class="badge bg-success">Đang học</span>
                                                        </div>
                                                        <button class="btn btn-sm btn-primary" onclick="showProfile()">
                                                            <i class="fas fa-eye me-1"></i>
                                                            Xem chi tiết
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 mb-4">
                                <div class="card student-card h-100">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-3 text-center">
                                                <div class="student-photo bg-secondary d-flex align-items-center justify-content-center text-white">
                                                    <i class="fas fa-user fa-2x"></i>
                                                </div>
                                            </div>
                                            <div class="col-9">
                                                <div class="student-info">
                                                    <h5>Trần Thị Bình</h5>
                                                    <p class="text-muted mb-1">
                                                        <i class="fas fa-id-card me-1"></i>
                                                        2051120002
                                                    </p>
                                                    <p class="text-muted mb-1">
                                                        <i class="fas fa-users me-1"></i>
                                                        Công nghệ thông tin 20A
                                                    </p>
                                                    <p class="text-muted mb-2">
                                                        <i class="fas fa-graduation-cap me-1"></i>
                                                        Công nghệ thông tin
                                                    </p>
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <div>
                                                            <span class="badge bg-pink">Nữ</span>
                                                            <span class="badge bg-success">Đang học</span>
                                                        </div>
                                                        <button class="btn btn-sm btn-primary" onclick="showProfile()">
                                                            <i class="fas fa-eye me-1"></i>
                                                            Xem chi tiết
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Hướng dẫn sử dụng -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            Hướng dẫn sử dụng
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Đây là phiên bản DEMO</h6>
                            <p class="mb-2">Để chạy đầy đủ chức năng, bạn cần:</p>
                            <ol>
                                <li>Cài đặt XAMPP (Apache + MySQL + PHP)</li>
                                <li>Import file database.sql vào MySQL</li>
                                <li>Cấu hình kết nối trong config.php</li>
                                <li>Chạy website qua localhost</li>
                            </ol>
                            <p class="mb-0">
                                <strong>Xem chi tiết trong file:</strong> 
                                <a href="SETUP_GUIDE.md" target="_blank">SETUP_GUIDE.md</a>
                            </p>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-id-card text-primary me-2"></i>Tìm theo mã sinh viên:</h6>
                                <p class="text-muted small">Nhập mã số sinh viên (VD: 2051120001)</p>
                                
                                <h6><i class="fas fa-user text-success me-2"></i>Tìm theo họ tên:</h6>
                                <p class="text-muted small">Nhập họ tên đầy đủ hoặc một phần (VD: Nguyễn Văn)</p>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-users text-warning me-2"></i>Tìm theo lớp:</h6>
                                <p class="text-muted small">Nhập mã lớp (VD: CNTT20A)</p>
                                
                                <h6><i class="fas fa-calendar text-info me-2"></i>Tìm theo khóa:</h6>
                                <p class="text-muted small">Nhập khóa học (VD: 2020-2024)</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-white text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0">&copy; 2024 Trường Đại học Trà Vinh. Phát triển bởi Khoa Kỹ thuật và Công nghệ.</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Toggle advanced search
        document.getElementById('toggleAdvanced').addEventListener('click', function() {
            const advancedSearch = document.getElementById('advanced_search');
            const isVisible = advancedSearch.style.display !== 'none';
            
            if (isVisible) {
                advancedSearch.style.display = 'none';
                this.innerHTML = '<i class="fas fa-cog me-1"></i>Tìm kiếm nâng cao';
            } else {
                advancedSearch.style.display = 'block';
                this.innerHTML = '<i class="fas fa-cog me-1"></i>Ẩn tìm kiếm nâng cao';
            }
        });

        // Dynamic placeholder
        document.getElementById('search_type').addEventListener('change', function() {
            const keyword = document.getElementById('keyword');
            const placeholders = {
                'student_code': 'VD: 2051120001',
                'full_name': 'VD: Nguyễn Văn An',
                'class': 'VD: CNTT20A',
                'academic_year': 'VD: 2020-2024'
            };
            
            keyword.placeholder = placeholders[this.value] || 'Nhập từ khóa...';
        });

        // Demo form submission
        document.getElementById('demoForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const searchType = document.getElementById('search_type').value;
            const keyword = document.getElementById('keyword').value.trim();
            
            if (!searchType || !keyword) {
                alert('Vui lòng chọn loại tìm kiếm và nhập từ khóa!');
                return;
            }
            
            // Show demo results
            document.getElementById('demoResults').style.display = 'block';
            document.getElementById('demoResults').scrollIntoView({ behavior: 'smooth' });
            
            // Show notification
            const notification = document.createElement('div');
            notification.className = 'alert alert-info alert-dismissible fade show mt-3';
            notification.innerHTML = `
                <i class="fas fa-info-circle me-2"></i>
                <strong>DEMO:</strong> Tìm kiếm "${keyword}" theo ${searchType}. 
                Trong phiên bản đầy đủ sẽ có kết quả thực từ database.
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.querySelector('.card-body').appendChild(notification);
            
            // Auto remove notification after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        });

        function showProfile() {
            alert('DEMO: Trong phiên bản đầy đủ, đây sẽ mở trang lý lịch trích ngang chi tiết của sinh viên với đầy đủ thông tin cá nhân, học tập và gia đình.');
        }
    </script>
</body>
</html>

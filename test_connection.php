<?php
/**
 * File test kết nối database
 * Hệ thống tra cứu lý lịch sinh viên - Trường Đại học Tr<PERSON>
 */

require_once 'config.php';

echo "<h2>Test kết nối database</h2>";

try {
    // Test kết nối
    echo "<p>✓ Kết nối database thành công!</p>";
    
    // Test truy vấn cơ bản
    $count_students = $database->count("SELECT COUNT(*) FROM students");
    $count_majors = $database->count("SELECT COUNT(*) FROM majors");
    $count_classes = $database->count("SELECT COUNT(*) FROM classes");
    $count_parents = $database->count("SELECT COUNT(*) FROM parent_info");
    
    echo "<h3>Thống kê dữ liệu:</h3>";
    echo "<ul>";
    echo "<li>Số sinh viên: <strong>{$count_students}</strong></li>";
    echo "<li>S<PERSON> ngành học: <strong>{$count_majors}</strong></li>";
    echo "<li>S<PERSON> lớp học: <strong>{$count_classes}</strong></li>";
    echo "<li>Số thông tin cha mẹ: <strong>{$count_parents}</strong></li>";
    echo "</ul>";
    
    // Test truy vấn chi tiết
    echo "<h3>Danh sách sinh viên mẫu:</h3>";
    $students = $database->fetchAll("
        SELECT s.student_code, s.full_name, c.class_name, m.major_name 
        FROM students s 
        LEFT JOIN classes c ON s.class_id = c.id 
        LEFT JOIN majors m ON s.major_id = m.id 
        ORDER BY s.student_code
    ");
    
    if ($students) {
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>Mã SV</th><th>Họ tên</th><th>Lớp</th><th>Ngành</th></tr>";
        foreach ($students as $student) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($student['student_code']) . "</td>";
            echo "<td>" . htmlspecialchars($student['full_name']) . "</td>";
            echo "<td>" . htmlspecialchars($student['class_name']) . "</td>";
            echo "<td>" . htmlspecialchars($student['major_name']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<p><strong>✓ Tất cả các test đều thành công!</strong></p>";
    echo "<p><a href='index.php'>Truy cập ứng dụng chính</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Lỗi: " . $e->getMessage() . "</p>";
    echo "<p>Vui lòng kiểm tra:</p>";
    echo "<ul>";
    echo "<li>Thông tin kết nối database trong config.php</li>";
    echo "<li>Database đã được tạo và import dữ liệu</li>";
    echo "<li>MySQL server đang chạy</li>";
    echo "<li>Extension PDO và PDO_MySQL đã được cài đặt</li>";
    echo "</ul>";
}
?>

#include "student_manager.h"
#include <algorithm>
#include <vector>

// Constructor
StudentManager::StudentManager() : head(nullptr) {}

// Destructor
StudentManager::~StudentManager() {
    clearList();
}

// Thêm sinh viên vào cuối danh sách
void StudentManager::addStudent(const Student& student) {
    Node* newNode = new Node(student);
    
    if (head == nullptr) {
        head = newNode;
    } else {
        Node* current = head;
        while (current->next != nullptr) {
            current = current->next;
        }
        current->next = newNode;
    }
}

// Thêm sinh viên vào danh sách đã sắp xếp theo mã sinh viên
void StudentManager::addStudentSorted(const Student& student) {
    Node* newNode = new Node(student);
    
    // Nếu danh sách rỗng hoặc sinh viên mới có mã nhỏ hơn head
    if (head == nullptr || student.studentID < head->data.studentID) {
        newNode->next = head;
        head = newNode;
        return;
    }
    
    // Tì<PERSON> vị trí thích hợp để chèn
    Node* current = head;
    while (current->next != nullptr && current->next->data.studentID < student.studentID) {
        current = current->next;
    }
    
    newNode->next = current->next;
    current->next = newNode;
}

// Xóa sinh viên theo mã số sinh viên
bool StudentManager::removeStudent(const string& studentID) {
    if (head == nullptr) return false;
    
    // Nếu sinh viên cần xóa là head
    if (head->data.studentID == studentID) {
        Node* temp = head;
        head = head->next;
        delete temp;
        return true;
    }
    
    // Tìm sinh viên cần xóa
    Node* current = head;
    while (current->next != nullptr && current->next->data.studentID != studentID) {
        current = current->next;
    }
    
    if (current->next != nullptr) {
        Node* temp = current->next;
        current->next = current->next->next;
        delete temp;
        return true;
    }
    
    return false;
}

// Sắp xếp danh sách theo mã số sinh viên (sử dụng bubble sort)
void StudentManager::sortByStudentID() {
    if (head == nullptr || head->next == nullptr) return;
    
    bool swapped;
    do {
        swapped = false;
        Node* current = head;
        
        while (current->next != nullptr) {
            if (current->data.studentID > current->next->data.studentID) {
                // Hoán đổi dữ liệu
                Student temp = current->data;
                current->data = current->next->data;
                current->next->data = temp;
                swapped = true;
            }
            current = current->next;
        }
    } while (swapped);
}

// Hiển thị tất cả sinh viên
void StudentManager::displayAllStudents() const {
    if (head == nullptr) {
        cout << "Danh sach sinh vien trong!" << endl;
        return;
    }
    
    displayHeader();
    Node* current = head;
    int count = 1;
    
    while (current != nullptr) {
        cout << setw(3) << count++;
        displayStudent(current->data);
        current = current->next;
    }
    cout << string(120, '-') << endl;
}

// Kiểm tra danh sách có rỗng không
bool StudentManager::isEmpty() const {
    return head == nullptr;
}

// Xóa toàn bộ danh sách
void StudentManager::clearList() {
    while (head != nullptr) {
        Node* temp = head;
        head = head->next;
        delete temp;
    }
}

// Tìm sinh viên theo mã số sinh viên
Node* StudentManager::findByStudentID(const string& studentID) const {
    Node* current = head;
    while (current != nullptr) {
        if (current->data.studentID == studentID) {
            return current;
        }
        current = current->next;
    }
    return nullptr;
}

// Tìm sinh viên theo mã lớp
void StudentManager::findByClassCode(const string& classCode) const {
    bool found = false;
    Node* current = head;

    cout << "Ket qua tim kiem theo ma lop: " << classCode << endl;
    displayHeader();

    int count = 1;
    while (current != nullptr) {
        if (current->data.classCode == classCode) {
            cout << setw(3) << count++;
            displayStudent(current->data);
            found = true;
        }
        current = current->next;
    }

    if (!found) {
        cout << "Khong tim thay sinh vien nao co ma lop: " << classCode << endl;
    }
    cout << string(120, '-') << endl;
}

// Tìm sinh viên theo năm sinh
void StudentManager::findByBirthYear(int year) const {
    bool found = false;
    Node* current = head;

    cout << "Ket qua tim kiem theo nam sinh: " << year << endl;
    displayHeader();

    int count = 1;
    while (current != nullptr) {
        if (current->data.birthYear == year) {
            cout << setw(3) << count++;
            displayStudent(current->data);
            found = true;
        }
        current = current->next;
    }

    if (!found) {
        cout << "Khong tim thay sinh vien nao sinh nam: " << year << endl;
    }
    cout << string(120, '-') << endl;
}

// Tìm sinh viên theo khoảng điểm
void StudentManager::findByScore(float minScore, float maxScore) const {
    bool found = false;
    Node* current = head;

    cout << "Ket qua tim kiem theo diem (" << minScore << " - " << maxScore << "):" << endl;
    displayHeader();

    int count = 1;
    while (current != nullptr) {
        if (current->data.admissionScore >= minScore && current->data.admissionScore <= maxScore) {
            cout << setw(3) << count++;
            displayStudent(current->data);
            found = true;
        }
        current = current->next;
    }

    if (!found) {
        cout << "Khong tim thay sinh vien nao co diem trong khoang: " << minScore << " - " << maxScore << endl;
    }
    cout << string(120, '-') << endl;
}

// Hiển thị thông tin một sinh viên
void StudentManager::displayStudent(const Student& student) const {
    cout << setw(12) << student.className
         << setw(10) << student.classCode
         << setw(15) << student.major
         << setw(20) << student.fullName
         << setw(12) << student.studentID
         << setw(8) << student.birthYear
         << setw(8) << fixed << setprecision(2) << student.admissionScore << endl;
}

// Hiển thị header của bảng
void StudentManager::displayHeader() const {
    cout << string(120, '-') << endl;
    cout << setw(3) << "STT"
         << setw(12) << "Ten lop"
         << setw(10) << "Ma lop"
         << setw(15) << "Nganh hoc"
         << setw(20) << "Ho ten SV"
         << setw(12) << "Ma SV"
         << setw(8) << "Nam sinh"
         << setw(8) << "Diem XT" << endl;
    cout << string(120, '-') << endl;
}

// Nhập danh sách sinh viên
void StudentManager::inputStudentList() {
    int n;
    cout << "Nhap so luong sinh vien: ";
    cin >> n;
    cin.ignore();

    for (int i = 0; i < n; i++) {
        cout << "\nNhap thong tin sinh vien thu " << (i + 1) << ":" << endl;
        Student student = createStudent();
        addStudent(student);
    }

    cout << "Da nhap thanh cong " << n << " sinh vien!" << endl;
}

// Nhập thông tin một sinh viên
void StudentManager::inputSingleStudent() {
    cout << "Nhap thong tin sinh vien moi:" << endl;
    Student student = createStudent();
    addStudentSorted(student);
    cout << "Da them sinh vien thanh cong!" << endl;
}

// Các hàm tiện ích
void clearScreen() {
    system("cls");
}

void pauseScreen() {
    cout << "\nNhan Enter de tiep tuc...";
    cin.ignore();
    cin.get();
}

Student createStudent() {
    Student student;

    cout << "Ten lop: ";
    getline(cin, student.className);

    cout << "Ma lop: ";
    getline(cin, student.classCode);

    cout << "Nganh hoc: ";
    getline(cin, student.major);

    cout << "Ho ten sinh vien: ";
    getline(cin, student.fullName);

    cout << "Ma so sinh vien: ";
    getline(cin, student.studentID);

    cout << "Nam sinh: ";
    cin >> student.birthYear;

    cout << "Diem xet tuyen: ";
    cin >> student.admissionScore;
    cin.ignore();

    return student;
}

// Lưu danh sách sinh viên vào file nhị phân
bool StudentManager::saveToFile(const string& filename) const {
    ofstream file(filename, ios::binary);
    if (!file.is_open()) {
        cout << "Khong the mo file de ghi: " << filename << endl;
        return false;
    }

    // Đếm số lượng sinh viên
    int count = 0;
    Node* current = head;
    while (current != nullptr) {
        count++;
        current = current->next;
    }

    // Ghi số lượng sinh viên
    file.write(reinterpret_cast<const char*>(&count), sizeof(count));

    // Ghi từng sinh viên
    current = head;
    while (current != nullptr) {
        const Student& student = current->data;

        // Ghi độ dài và nội dung của từng string
        size_t len;

        len = student.className.length();
        file.write(reinterpret_cast<const char*>(&len), sizeof(len));
        file.write(student.className.c_str(), len);

        len = student.classCode.length();
        file.write(reinterpret_cast<const char*>(&len), sizeof(len));
        file.write(student.classCode.c_str(), len);

        len = student.major.length();
        file.write(reinterpret_cast<const char*>(&len), sizeof(len));
        file.write(student.major.c_str(), len);

        len = student.fullName.length();
        file.write(reinterpret_cast<const char*>(&len), sizeof(len));
        file.write(student.fullName.c_str(), len);

        len = student.studentID.length();
        file.write(reinterpret_cast<const char*>(&len), sizeof(len));
        file.write(student.studentID.c_str(), len);

        // Ghi các dữ liệu số
        file.write(reinterpret_cast<const char*>(&student.birthYear), sizeof(student.birthYear));
        file.write(reinterpret_cast<const char*>(&student.admissionScore), sizeof(student.admissionScore));

        current = current->next;
    }

    file.close();
    cout << "Da luu " << count << " sinh vien vao file: " << filename << endl;
    return true;
}

// Đọc danh sách sinh viên từ file nhị phân
bool StudentManager::loadFromFile(const string& filename) {
    ifstream file(filename, ios::binary);
    if (!file.is_open()) {
        cout << "Khong the mo file de doc: " << filename << endl;
        return false;
    }

    // Xóa danh sách hiện tại
    clearList();

    // Đọc số lượng sinh viên
    int count;
    file.read(reinterpret_cast<char*>(&count), sizeof(count));

    // Đọc từng sinh viên
    for (int i = 0; i < count; i++) {
        Student student;
        size_t len;
        char buffer[1000];

        // Đọc className
        file.read(reinterpret_cast<char*>(&len), sizeof(len));
        file.read(buffer, len);
        buffer[len] = '\0';
        student.className = string(buffer);

        // Đọc classCode
        file.read(reinterpret_cast<char*>(&len), sizeof(len));
        file.read(buffer, len);
        buffer[len] = '\0';
        student.classCode = string(buffer);

        // Đọc major
        file.read(reinterpret_cast<char*>(&len), sizeof(len));
        file.read(buffer, len);
        buffer[len] = '\0';
        student.major = string(buffer);

        // Đọc fullName
        file.read(reinterpret_cast<char*>(&len), sizeof(len));
        file.read(buffer, len);
        buffer[len] = '\0';
        student.fullName = string(buffer);

        // Đọc studentID
        file.read(reinterpret_cast<char*>(&len), sizeof(len));
        file.read(buffer, len);
        buffer[len] = '\0';
        student.studentID = string(buffer);

        // Đọc các dữ liệu số
        file.read(reinterpret_cast<char*>(&student.birthYear), sizeof(student.birthYear));
        file.read(reinterpret_cast<char*>(&student.admissionScore), sizeof(student.admissionScore));

        addStudent(student);
    }

    file.close();
    cout << "Da doc " << count << " sinh vien tu file: " << filename << endl;
    return true;
}

// Hiển thị menu chính
void displayMenu() {
    cout << "\n" << string(60, '=') << endl;
    cout << "         CHUONG TRINH QUAN LY SINH VIEN" << endl;
    cout << string(60, '=') << endl;
    cout << "1. Nhap danh sach sinh vien" << endl;
    cout << "2. Xuat danh sach sinh vien" << endl;
    cout << "3. Them mot sinh vien (da sap xep)" << endl;
    cout << "4. Xoa sinh vien theo ma so" << endl;
    cout << "5. Sap xep danh sach theo ma sinh vien" << endl;
    cout << "6. Tim kiem sinh vien theo ma so" << endl;
    cout << "7. Tim kiem sinh vien theo ma lop" << endl;
    cout << "8. Tim kiem sinh vien theo nam sinh" << endl;
    cout << "9. Tim kiem sinh vien theo diem so" << endl;
    cout << "10. Luu danh sach vao file" << endl;
    cout << "11. Doc danh sach tu file" << endl;
    cout << "0. Thoat chuong trinh" << endl;
    cout << string(60, '-') << endl;
    cout << "Lua chon cua ban: ";
}

// Lấy lựa chọn từ người dùng
int getChoice() {
    int choice;
    cin >> choice;
    cin.ignore(); // Xóa ký tự newline còn lại
    return choice;
}

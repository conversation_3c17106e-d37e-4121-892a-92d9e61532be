/* 
 * Stylesheet cho hệ thống tra cứu lý lịch sinh viên
 * Trường <PERSON>c <PERSON>
 */

/* Reset và base styles */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

/* Header styles */
header {
    background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.logo {
    max-height: 60px;
    width: auto;
}

/* Card styles */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
    border-bottom: 2px solid #e9ecef;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

/* Form styles */
.form-control, .form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    transform: translateY(-1px);
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

/* Button styles */
.btn {
    border-radius: 10px;
    padding: 12px 24px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.btn-primary {
    background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);
    border: none;
}

.btn-outline-secondary {
    border: 2px solid #6c757d;
    color: #6c757d;
}

.btn-outline-secondary:hover {
    background: #6c757d;
    border-color: #6c757d;
}

.btn-outline-warning {
    border: 2px solid #ffc107;
    color: #ffc107;
}

.btn-outline-warning:hover {
    background: #ffc107;
    border-color: #ffc107;
    color: #000;
}

/* Search results styles */
.student-card {
    border-left: 4px solid #0d6efd;
    transition: all 0.3s ease;
}

.student-card:hover {
    border-left-color: #0b5ed7;
    background-color: #f8f9ff;
}

.student-photo {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 50%;
    border: 3px solid #e9ecef;
    transition: all 0.3s ease;
}

.student-photo:hover {
    border-color: #0d6efd;
    transform: scale(1.05);
}

.student-info h5 {
    color: #0d6efd;
    margin-bottom: 5px;
}

.student-info .text-muted {
    font-size: 0.9em;
}

.badge {
    font-size: 0.8em;
    padding: 6px 12px;
    border-radius: 20px;
}

/* Profile page styles */
.profile-header {
    background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);
    color: white;
    border-radius: 15px 15px 0 0;
    padding: 30px;
}

.profile-photo {
    width: 150px;
    height: 150px;
    object-fit: cover;
    border-radius: 50%;
    border: 5px solid white;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.info-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.info-section h6 {
    color: #0d6efd;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
    margin-bottom: 15px;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.info-row:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 600;
    color: #495057;
    min-width: 150px;
}

.info-value {
    color: #212529;
    text-align: right;
}

/* Pagination styles */
.pagination .page-link {
    border-radius: 10px;
    margin: 0 2px;
    border: 2px solid #e9ecef;
    color: #0d6efd;
    transition: all 0.3s ease;
}

.pagination .page-link:hover {
    background-color: #0d6efd;
    border-color: #0d6efd;
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* Alert styles */
.alert {
    border-radius: 10px;
    border: none;
    padding: 15px 20px;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

/* Footer styles */
footer {
    background: linear-gradient(135deg, #343a40 0%, #212529 100%);
    margin-top: auto;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .card {
        margin: 10px 0;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .student-card {
        text-align: center;
    }
    
    .student-photo {
        margin-bottom: 15px;
    }
    
    .profile-header {
        text-align: center;
        padding: 20px;
    }
    
    .profile-photo {
        width: 120px;
        height: 120px;
        margin-bottom: 20px;
    }
    
    .info-row {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .info-value {
        text-align: left;
        margin-top: 5px;
    }
}

/* Animation classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Loading spinner */
.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #0d6efd;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .btn {
        display: none;
    }
}

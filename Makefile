# Makefile for Student Management System

CXX = g++
CXXFLAGS = -std=c++11 -Wall -Wextra
TARGET = student_manager
SOURCES = main.cpp student_manager.cpp
OBJECTS = $(SOURCES:.cpp=.o)

# Default target
all: $(TARGET)

# Build the executable
$(TARGET): $(OBJECTS)
	$(CXX) $(CXXFLAGS) -o $(TARGET) $(OBJECTS)

# Build object files
%.o: %.cpp
	$(CXX) $(CXXFLAGS) -c $< -o $@

# Clean build files
clean:
	del /Q *.o $(TARGET).exe 2>nul || true

# Run the program
run: $(TARGET)
	./$(TARGET)

# Install dependencies (if needed)
install:
	@echo "No external dependencies required"

.PHONY: all clean run install

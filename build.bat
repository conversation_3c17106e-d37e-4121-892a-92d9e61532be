@echo off
echo Building Student Management System...

REM Check if g++ is available
where g++ >nul 2>nul
if %ERRORLEVEL% == 0 (
    echo Using g++ compiler...
    g++ -std=c++11 -Wall -Wextra -o student_manager.exe main.cpp student_manager.cpp
    if %ERRORLEVEL% == 0 (
        echo Build successful! Run student_manager.exe to start the program.
    ) else (
        echo Build failed with g++!
    )
    goto :end
)

REM Check if cl is available
where cl >nul 2>nul
if %ERRORLEVEL% == 0 (
    echo Using Microsoft Visual C++ compiler...
    cl /EHsc /Fe:student_manager.exe main.cpp student_manager.cpp
    if %ERRORLEVEL% == 0 (
        echo Build successful! Run student_manager.exe to start the program.
        del *.obj >nul 2>nul
    ) else (
        echo Build failed with cl!
    )
    goto :end
)

REM Check if clang++ is available
where clang++ >nul 2>nul
if %ERRORLEVEL% == 0 (
    echo Using clang++ compiler...
    clang++ -std=c++11 -Wall -Wextra -o student_manager.exe main.cpp student_manager.cpp
    if %ERRORLEVEL% == 0 (
        echo Build successful! Run student_manager.exe to start the program.
    ) else (
        echo Build failed with clang++!
    )
    goto :end
)

echo No C++ compiler found! Please install one of the following:
echo - MinGW-w64 (for g++)
echo - Microsoft Visual Studio (for cl)
echo - LLVM/Clang (for clang++)

:end
pause
